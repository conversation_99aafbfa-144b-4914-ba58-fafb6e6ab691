# Monitor Card Chart Types Documentation

## Overview

This document describes the different chart types available in the MonitorCard component and their appropriate use cases for displaying time series data in monitoring dashboards.

## Chart Types

### 1. Area Chart (`area`)
**Best for:** Network traffic, bandwidth utilization, cumulative metrics

**Characteristics:**
- Filled area under the curve
- Good for showing volume/magnitude
- Emphasizes the "amount" of data
- Works well with gradient fills

**Use Cases:**
- Network bandwidth (inMbps/outMbps)
- Memory usage over time
- Disk I/O volume
- Request volume trends

**Visual Example:** Blue gradient fill showing network traffic patterns

### 2. Line Chart (`line`)
**Best for:** Transaction rates, response times, success rates

**Characteristics:**
- Clean, minimal line representation
- Good for showing trends and patterns
- Easy to read precise values
- Multiple lines can be overlaid

**Use Cases:**
- Transaction per second (TPS)
- Response time trends
- Success/error rates
- Temperature monitoring

**Visual Example:** Orange line showing transaction volume over time

### 3. Bar Chart (`bar`)
**Best for:** Discrete events, request counts, categorical data

**Characteristics:**
- Discrete vertical bars
- Good for comparing values across time periods
- Shows individual data points clearly
- Emphasizes specific moments/events

**Use Cases:**
- Request counts per minute
- Error counts
- Event occurrences
- Batch job completions

**Visual Example:** Green bars showing request volume spikes

### 4. Scatter Plot (`scatter`)
**Best for:** Latency distribution, correlation analysis, outlier detection

**Characteristics:**
- Individual data points as dots
- Good for showing distribution patterns
- Helps identify outliers and clusters
- Shows data density

**Use Cases:**
- Response time distribution
- Latency patterns
- Performance outliers
- Correlation between metrics

**Visual Example:** Purple dots showing RTT distribution patterns

### 5. Step Chart (`step`)
**Best for:** State changes, threshold monitoring, discrete levels

**Characteristics:**
- Step-like transitions between values
- Good for showing state changes
- Emphasizes discrete levels
- Clear transitions between states

**Use Cases:**
- Service availability states
- Configuration changes
- Threshold crossings
- Status level changes

**Visual Example:** Cyan steps showing success rate levels

### 6. Composed Chart (`composed`)
**Best for:** Multi-metric correlation, complex relationships

**Characteristics:**
- Combines bars and lines
- Shows multiple metrics simultaneously
- Good for correlation analysis
- More complex but information-rich

**Use Cases:**
- Request volume (bars) + response time (line)
- Throughput vs latency correlation
- Multi-dimensional monitoring
- Performance relationship analysis

**Visual Example:** Orange bars (requests) + red line (RTT)

## Data Patterns

### Normal Pattern (`normal`)
- Standard variation with realistic noise
- Simulates typical daily patterns
- Good baseline for comparison

### Spike Pattern (`spike`)
- Sharp increase in the middle of the timeline
- Simulates traffic spikes or incidents
- Good for testing alert visualization

### Step Pattern (`step`)
- Distinct level changes
- Simulates configuration changes or capacity adjustments
- Good for showing discrete state transitions

### Oscillating Pattern (`oscillating`)
- Sine wave-like variations
- Simulates cyclical patterns
- Good for showing periodic behavior

### Declining Pattern (`declining`)
- Gradual decrease over time
- Simulates degradation or capacity reduction
- Good for showing performance decline

### Recovering Pattern (`recovering`)
- Improvement from poor to good state
- Simulates incident recovery
- Good for showing system healing

## Implementation Guidelines

### Chart Selection Criteria

1. **Data Type Consideration:**
   - Continuous metrics → Line/Area charts
   - Discrete events → Bar charts
   - Distribution data → Scatter plots
   - State changes → Step charts

2. **Visual Clarity:**
   - Single metric → Line/Area/Bar
   - Multiple metrics → Composed charts
   - Pattern emphasis → Choose based on data characteristics

3. **User Intent:**
   - Trend analysis → Line charts
   - Volume emphasis → Area/Bar charts
   - Outlier detection → Scatter plots
   - State monitoring → Step charts

### Configuration Example

```typescript
const testCard: MonitorCardData = {
  id: "test-example",
  name: "[TEST] Service Monitor",
  type: "network",
  showMetrics: true,
  chartType: "area",        // Chart visualization type
  dataPattern: "spike",     // Data pattern for testing
  iconColor: "blue",
  statusColor: "green"
}
```

## UI Design Evaluation

The TEST cards in the Monitor page showcase different chart types to help evaluate:

1. **Visual Hierarchy:** How different chart types affect card layout
2. **Information Density:** Which charts convey the most useful information
3. **Readability:** How easy it is to interpret different visualizations
4. **Aesthetic Appeal:** Which chart types fit best with the overall design

## Best Practices

1. **Consistency:** Use similar chart types for similar metrics across the dashboard
2. **Context:** Choose chart types that match the user's mental model of the data
3. **Performance:** Consider rendering performance for real-time updates
4. **Accessibility:** Ensure charts are readable and provide alternative text descriptions

## Future Enhancements

- **Interactive Charts:** Add hover tooltips and click interactions
- **Real-time Updates:** Implement live data streaming
- **Custom Patterns:** Allow users to define custom data patterns
- **Chart Combinations:** Support more complex chart combinations
- **Responsive Design:** Optimize charts for different screen sizes
