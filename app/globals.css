@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --font-sans: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

:root {
  /* Updated color tokens to match modern AI-era design brief with cyan primary and clean neutrals */
  --background: #f9fafb; /* Light background for dashboard */
  --foreground: #4b5563; /* Dark foreground for text */
  --card: #ffffff; /* Background for cards */
  --card-foreground: #4b5563; /* Text color on cards */
  --popover: #ffffff; /* Background for popovers */
  --popover-foreground: #4b5563; /* Text color on popovers */
  --primary: #0891b2; /* Primary action color for buttons */
  --primary-foreground: #ffffff; /* Text color on primary buttons */
  --secondary: #0ea5e9; /* Secondary action color */
  --secondary-foreground: #ffffff; /* Text color on secondary actions */
  --muted: #f9fafb; /* Background for muted sections */
  --muted-foreground: #4b5563; /* Text color for muted sections */
  --accent: #0ea5e9; /* Accent color for highlights */
  --accent-foreground: #ffffff; /* Text color on accent elements */
  --destructive: #be123c; /* Color for alerts or errors */
  --destructive-foreground: #ffffff; /* Text color for alerts */
  --border: #e5e7eb; /* Light border for cards */
  --input: #ffffff; /* Input field background */
  --ring: #0891b2; /* Focus ring color */
  --chart-1: #60a5fa; /* Color for first data series */
  --chart-2: #3b82f6; /* Color for second data series */
  --chart-3: #2563eb; /* Color for third data series */
  --chart-4: #1d4ed8; /* Color for fourth data series */
  --chart-5: #1e40af; /* Color for fifth data series */
  --radius: 0.5rem; /* Corner rounding for cards */

  /* New Corner Radius System - Material Design 3 Inspired */
  /* Prefix: 'corner-' to distinguish from legacy 'radius-' system */
  --corner-base: 1rem; /* 16px - User configurable base value (Large) */

  /* Complete corner radius scale */
  --corner-none: 0;                                      /* 0dp - No rounding */
  --corner-xs: 0.25rem;                                 /* 4dp - Extra small */
  --corner-sm: 0.5rem;                                  /* 8dp - Small */
  --corner-md: 0.75rem;                                 /* 12dp - Medium */
  --corner-lg: var(--corner-base);                      /* 16dp - Large (base) */
  --corner-lg-plus: calc(var(--corner-base) + 0.25rem); /* 20dp - Large increased */
  --corner-xl: calc(var(--corner-base) + 0.75rem);      /* 28dp - Extra large */
  --corner-xl-plus: calc(var(--corner-base) + 1rem);    /* 32dp - Extra large increased */
  --corner-2xl: calc(var(--corner-base) + 2rem);        /* 48dp - Extra extra large */
  --corner-full: 9999px;                                /* Fully rounded corners */

  --sidebar: #ffffff; /* Background for sidebar */
  --sidebar-foreground: #4b5563; /* Text color for sidebar */
  --sidebar-primary: #0891b2; /* Primary action color for sidebar */
  --sidebar-primary-foreground: #ffffff; /* Text color for primary actions */
  --sidebar-accent: #0ea5e9; /* Accent color for sidebar highlights */
  --sidebar-accent-foreground: #ffffff; /* Text color for accent items */
  --sidebar-border: #e5e7eb; /* Border color for sidebar */
  --sidebar-ring: #0891b2; /* Focus ring for sidebar elements */
}

.dark {
  /* Updated dark mode tokens to maintain consistency with AI-era design */
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #0ea5e9;
  --primary-foreground: #0f172a;
  --secondary: #334155;
  --secondary-foreground: #f1f5f9;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #0ea5e9;
  --accent-foreground: #0f172a;
  --destructive: #dc2626;
  --destructive-foreground: #f1f5f9;
  --border: #334155;
  --input: #334155;
  --ring: #0ea5e9;
  --chart-1: #60a5fa;
  --chart-2: #3b82f6;
  --chart-3: #2563eb;
  --chart-4: #1d4ed8;
  --chart-5: #1e40af;
  --sidebar: #1e293b;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #0ea5e9;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f1f5f9;
  --sidebar-border: #334155;
  --sidebar-ring: #0ea5e9;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* New Corner Radius System - Tailwind Class Mappings */
  /* Use 'corner-' prefix for new system classes */
  --corner-none: var(--corner-none);
  --corner-xs: var(--corner-xs);
  --corner-sm: var(--corner-sm);
  --corner-md: var(--corner-md);
  --corner-lg: var(--corner-lg);
  --corner-lg-plus: var(--corner-lg-plus);
  --corner-xl: var(--corner-xl);
  --corner-xl-plus: var(--corner-xl-plus);
  --corner-2xl: var(--corner-2xl);
  --corner-full: var(--corner-full);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Cursor Pointer Consistency Layer */
@layer components {
  /* Ensure all interactive elements have proper cursor styling */
  button,
  [role="button"],
  [type="button"],
  [type="submit"],
  [type="reset"],
  input[type="checkbox"],
  input[type="radio"],
  select,
  summary,
  a[href],
  label[for],
  [tabindex]:not([tabindex="-1"]),
  [data-clickable="true"],
  [onClick] {
    @apply cursor-pointer;
  }

  /* Disabled states should show not-allowed cursor */
  button:disabled,
  [role="button"]:disabled,
  [type="button"]:disabled,
  [type="submit"]:disabled,
  [type="reset"]:disabled,
  input:disabled,
  select:disabled,
  [aria-disabled="true"] {
    @apply cursor-not-allowed;
  }

  /* Interactive cards and clickable containers */
  [data-slot="card"][onClick],
  [data-slot="card"][data-clickable="true"],
  .cursor-pointer {
    @apply cursor-pointer;
  }

  /* Ensure proper cursor for form elements */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="url"],
  input[type="tel"],
  input[type="number"],
  textarea {
    @apply cursor-text;
  }

  /* Utility classes for explicit cursor control */
  .clickable {
    @apply cursor-pointer;
  }

  .not-clickable {
    @apply cursor-default;
  }

  /* New Corner Radius System - Utility Classes */
  /* Use 'corner-' prefix to distinguish from legacy 'rounded-' classes */
  .corner-none {
    border-radius: var(--corner-none);
  }

  .corner-xs {
    border-radius: var(--corner-xs);
  }

  .corner-sm {
    border-radius: var(--corner-sm);
  }

  .corner-md {
    border-radius: var(--corner-md);
  }

  .corner-lg {
    border-radius: var(--corner-lg);
  }

  .corner-lg-plus {
    border-radius: var(--corner-lg-plus);
  }

  .corner-xl {
    border-radius: var(--corner-xl);
  }

  .corner-xl-plus {
    border-radius: var(--corner-xl-plus);
  }

  .corner-2xl {
    border-radius: var(--corner-2xl);
  }

  .corner-full {
    border-radius: var(--corner-full);
  }

  /* Corner radius variants for specific sides - improved formatting */
  .corner-t-none {
    border-top-left-radius: var(--corner-none);
    border-top-right-radius: var(--corner-none);
  }
  .corner-t-xs {
    border-top-left-radius: var(--corner-xs);
    border-top-right-radius: var(--corner-xs);
  }
  .corner-t-sm {
    border-top-left-radius: var(--corner-sm);
    border-top-right-radius: var(--corner-sm);
  }
  .corner-t-md {
    border-top-left-radius: var(--corner-md);
    border-top-right-radius: var(--corner-md);
  }
  .corner-t-lg {
    border-top-left-radius: var(--corner-lg);
    border-top-right-radius: var(--corner-lg);
  }
  .corner-t-lg-plus {
    border-top-left-radius: var(--corner-lg-plus);
    border-top-right-radius: var(--corner-lg-plus);
  }
  .corner-t-xl {
    border-top-left-radius: var(--corner-xl);
    border-top-right-radius: var(--corner-xl);
  }
  .corner-t-xl-plus {
    border-top-left-radius: var(--corner-xl-plus);
    border-top-right-radius: var(--corner-xl-plus);
  }
  .corner-t-2xl {
    border-top-left-radius: var(--corner-2xl);
    border-top-right-radius: var(--corner-2xl);
  }
  .corner-t-full {
    border-top-left-radius: var(--corner-full);
    border-top-right-radius: var(--corner-full);
  }

  .corner-b-none { border-bottom-left-radius: var(--corner-none); border-bottom-right-radius: var(--corner-none); }
  .corner-b-xs { border-bottom-left-radius: var(--corner-xs); border-bottom-right-radius: var(--corner-xs); }
  .corner-b-sm { border-bottom-left-radius: var(--corner-sm); border-bottom-right-radius: var(--corner-sm); }
  .corner-b-md { border-bottom-left-radius: var(--corner-md); border-bottom-right-radius: var(--corner-md); }
  .corner-b-lg { border-bottom-left-radius: var(--corner-lg); border-bottom-right-radius: var(--corner-lg); }
  .corner-b-lg-plus { border-bottom-left-radius: var(--corner-lg-plus); border-bottom-right-radius: var(--corner-lg-plus); }
  .corner-b-xl { border-bottom-left-radius: var(--corner-xl); border-bottom-right-radius: var(--corner-xl); }
  .corner-b-xl-plus { border-bottom-left-radius: var(--corner-xl-plus); border-bottom-right-radius: var(--corner-xl-plus); }
  .corner-b-2xl { border-bottom-left-radius: var(--corner-2xl); border-bottom-right-radius: var(--corner-2xl); }
  .corner-b-full { border-bottom-left-radius: var(--corner-full); border-bottom-right-radius: var(--corner-full); }

  .corner-l-none { border-top-left-radius: var(--corner-none); border-bottom-left-radius: var(--corner-none); }
  .corner-l-xs { border-top-left-radius: var(--corner-xs); border-bottom-left-radius: var(--corner-xs); }
  .corner-l-sm { border-top-left-radius: var(--corner-sm); border-bottom-left-radius: var(--corner-sm); }
  .corner-l-md { border-top-left-radius: var(--corner-md); border-bottom-left-radius: var(--corner-md); }
  .corner-l-lg { border-top-left-radius: var(--corner-lg); border-bottom-left-radius: var(--corner-lg); }
  .corner-l-lg-plus { border-top-left-radius: var(--corner-lg-plus); border-bottom-left-radius: var(--corner-lg-plus); }
  .corner-l-xl { border-top-left-radius: var(--corner-xl); border-bottom-left-radius: var(--corner-xl); }
  .corner-l-xl-plus { border-top-left-radius: var(--corner-xl-plus); border-bottom-left-radius: var(--corner-xl-plus); }
  .corner-l-2xl { border-top-left-radius: var(--corner-2xl); border-bottom-left-radius: var(--corner-2xl); }
  .corner-l-full { border-top-left-radius: var(--corner-full); border-bottom-left-radius: var(--corner-full); }

  .corner-r-none { border-top-right-radius: var(--corner-none); border-bottom-right-radius: var(--corner-none); }
  .corner-r-xs { border-top-right-radius: var(--corner-xs); border-bottom-right-radius: var(--corner-xs); }
  .corner-r-sm { border-top-right-radius: var(--corner-sm); border-bottom-right-radius: var(--corner-sm); }
  .corner-r-md { border-top-right-radius: var(--corner-md); border-bottom-right-radius: var(--corner-md); }
  .corner-r-lg { border-top-right-radius: var(--corner-lg); border-bottom-right-radius: var(--corner-lg); }
  .corner-r-lg-plus { border-top-right-radius: var(--corner-lg-plus); border-bottom-right-radius: var(--corner-lg-plus); }
  .corner-r-xl { border-top-right-radius: var(--corner-xl); border-bottom-right-radius: var(--corner-xl); }
  .corner-r-xl-plus { border-top-right-radius: var(--corner-xl-plus); border-bottom-right-radius: var(--corner-xl-plus); }
  .corner-r-2xl { border-top-right-radius: var(--corner-2xl); border-bottom-right-radius: var(--corner-2xl); }
  .corner-r-full { border-top-right-radius: var(--corner-full); border-bottom-right-radius: var(--corner-full); }
}

/* Component-specific styles */
@layer components {
  /* Utility: single-line text with gradient fade at right edge */
  .text-singleline-fade {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    /* Create a right-edge fade using a gradient mask; WebKit supports mask-image */
    -webkit-mask-image: linear-gradient(to right, black 80%, transparent 100%);
    mask-image: linear-gradient(to right, black 80%, transparent 100%);
  }

  /* CSS 动画相关代码已清理 */
  /* Monitor Card Component - using rem for better scalability */
  .monitor-card-size {
    width: 20rem;  /* 320px / 16px = 20rem */
    height: 15rem; /* 240px / 16px = 15rem */
  }

  /* Monitor Grid Layout Component */
  .monitor-grid-responsive {
    display: grid;
    gap: 1rem; /* Use rem units for consistency */
    justify-content: center;

    /* Dynamic grid columns based on available space */
    grid-template-columns: repeat(auto-fit, 20rem); /* 320px = 20rem */

    /* Calculate available width: viewport - sidebar - padding */
    width: calc(100vw - 3rem - 3rem); /* 48px = 3rem */
    margin: 0 auto;

    /* Ensure minimum spacing from container edges */
    padding: 0 1rem;
    box-sizing: border-box;
  }
}
